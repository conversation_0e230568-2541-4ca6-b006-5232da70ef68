<template>
  <div class="agent-list-page">
    <!-- 页面头部 -->
    <Card class="mb-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">代理商列表</h1>
          <p class="text-gray-600 mt-1">管理和查看所有代理商信息</p>
        </div>
        <Button type="primary" @click="showAddModal">
          <Icon icon="lucide:plus" class="mr-1" />
          新增代理商
        </Button>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-4">
        <Row :gutter="16">
          <Col :span="6">
            <Input
              v-model:value="queryParams.agentName"
              placeholder="代理商名称"
              allow-clear
              @press-enter="handleSearch"
            />
          </Col>
          <Col :span="6">
            <Input
              v-model:value="queryParams.contactPhone"
              placeholder="联系电话"
              allow-clear
              @press-enter="handleSearch"
            />
          </Col>
          <Col :span="6">
            <Select
              v-model:value="queryParams.status"
              placeholder="状态"
              allow-clear
              :options="statusOptions"
            />
          </Col>
          <Col :span="6">
            <Space>
              <Button type="primary" @click="handleSearch">
                <Icon icon="lucide:search" class="mr-1" />
                搜索
              </Button>
              <Button @click="handleReset">
                <Icon icon="lucide:refresh-cw" class="mr-1" />
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    </Card>

    <!-- 代理商列表 -->
    <Card>
      <Table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="agentId"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'agentName'">
            <div class="flex items-center">
              <Icon icon="lucide:building" class="mr-2 text-blue-500" />
              <div>
                <div class="font-medium">{{ record.agentName }}</div>
                <div class="text-sm text-gray-500">ID: {{ record.agentId }}</div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'status'">
            <Tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </Tag>
          </template>

          <template v-else-if="column.key === 'memberCount'">
            <div class="flex items-center">
              <Icon icon="lucide:users" class="mr-1 text-gray-400" />
              <span>{{ record.memberCount || 0 }}人</span>
            </div>
          </template>

          <template v-else-if="column.key === 'action'">
            <Space>
              <Button size="small" type="text" @click="viewAgent(record)">
                <Icon icon="lucide:eye" />
              </Button>
              <Button size="small" type="text" @click="editAgent(record)">
                <Icon icon="lucide:edit" />
              </Button>
              <Button 
                size="small" 
                type="text" 
                :danger="record.status === 1"
                @click="toggleStatus(record)"
              >
                <Icon :icon="record.status === 1 ? 'lucide:ban' : 'lucide:check'" />
              </Button>
              <Button size="small" type="text" danger @click="deleteAgent(record)">
                <Icon icon="lucide:trash-2" />
              </Button>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 新增/编辑代理商弹窗 -->
    <Modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑代理商' : '新增代理商'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="代理商名称" name="agentName">
              <Input v-model:value="formData.agentName" placeholder="请输入代理商名称" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="联系人" name="contactName">
              <Input v-model:value="formData.contactName" placeholder="请输入联系人姓名" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <Input v-model:value="formData.contactPhone" placeholder="请输入联系电话" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="邮箱" name="email">
              <Input v-model:value="formData.email" placeholder="请输入邮箱地址" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="地址" name="address">
              <Input v-model:value="formData.address" placeholder="请输入地址" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="状态" name="status">
              <Select v-model:value="formData.status" :options="statusOptions" />
            </FormItem>
          </Col>
        </Row>
        <FormItem label="备注" name="remark">
          <Input.TextArea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="3" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  Card,
  Button,
  Input,
  Select,
  Table,
  Tag,
  Space,
  Row,
  Col,
  Modal,
  Form,
  FormItem,
  message
} from 'ant-design-vue';
import { Icon } from '@iconify/vue';

// 响应式数据
const loading = ref(false);
const dataSource = ref([]);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive({
  agentName: '',
  contactPhone: '',
  status: undefined,
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 表格列配置
const columns = [
  {
    title: '代理商名称',
    dataIndex: 'agentName',
    key: 'agentName',
    width: 200,
  },
  {
    title: '联系人',
    dataIndex: 'contactName',
    key: 'contactName',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    key: 'contactPhone',
    width: 140,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180,
  },
  {
    title: '成员数量',
    dataIndex: 'memberCount',
    key: 'memberCount',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
];

// 表单数据
const formData = reactive({
  agentId: '',
  agentName: '',
  contactName: '',
  contactPhone: '',
  email: '',
  address: '',
  status: 1,
  remark: '',
});

// 表单验证规则
const formRules = {
  agentName: [
    { required: true, message: '请输入代理商名称', trigger: 'blur' },
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // TODO: 调用真实API
    // const response = await getAgentList({
    //   pageNum: pagination.current,
    //   pageSize: pagination.pageSize,
    //   ...queryParams,
    // });
    
    // 模拟数据
    const mockData = [
      {
        agentId: 'AGENT001',
        agentName: '华南教育科技有限公司',
        contactName: '张经理',
        contactPhone: '13800138001',
        email: '<EMAIL>',
        address: '广东省深圳市南山区',
        memberCount: 25,
        status: 1,
        createTime: '2024-01-15 10:30:00',
        remark: '华南地区主要代理商',
      },
      {
        agentId: 'AGENT002',
        agentName: '北京智慧教育集团',
        contactName: '李总',
        contactPhone: '13800138002',
        email: '<EMAIL>',
        address: '北京市海淀区中关村',
        memberCount: 18,
        status: 1,
        createTime: '2024-01-20 14:20:00',
        remark: '北京地区重点合作伙伴',
      },
    ];
    
    dataSource.value = mockData;
    pagination.total = mockData.length;
  } catch (error) {
    console.error('加载代理商列表失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置
const handleReset = () => {
  Object.assign(queryParams, {
    agentName: '',
    contactPhone: '',
    status: undefined,
  });
  pagination.current = 1;
  loadData();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

// 显示新增弹窗
const showAddModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

// 查看代理商
const viewAgent = (record: any) => {
  message.info(`查看代理商: ${record.agentName}`);
};

// 编辑代理商
const editAgent = (record: any) => {
  isEdit.value = true;
  Object.assign(formData, record);
  modalVisible.value = true;
};

// 切换状态
const toggleStatus = (record: any) => {
  const action = record.status === 1 ? '禁用' : '启用';
  Modal.confirm({
    title: `确认${action}`,
    content: `确定要${action}代理商"${record.agentName}"吗？`,
    onOk: async () => {
      try {
        // TODO: 调用真实API
        record.status = record.status === 1 ? 0 : 1;
        message.success(`${action}成功`);
      } catch (error) {
        message.error(`${action}失败`);
      }
    },
  });
};

// 删除代理商
const deleteAgent = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除代理商"${record.agentName}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        // TODO: 调用真实API
        const index = dataSource.value.findIndex((item: any) => item.agentId === record.agentId);
        if (index > -1) {
          dataSource.value.splice(index, 1);
        }
        message.success('删除成功');
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    // TODO: 调用真实API
    message.success(isEdit.value ? '编辑成功' : '新增成功');
    modalVisible.value = false;
    loadData();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 取消
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    agentId: '',
    agentName: '',
    contactName: '',
    contactPhone: '',
    email: '',
    address: '',
    status: 1,
    remark: '',
  });
  formRef.value?.resetFields();
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.agent-list-page {
  padding: 24px;
}
</style>
