<template>
  <div class="agent-list-page">
    <!-- 页面头部 -->
    <Card class="mb-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">代理商列表</h1>
          <p class="text-gray-600 mt-1">管理和查看所有代理商信息</p>
        </div>
        <Button type="primary" @click="showAddModal">
          <Icon icon="lucide:plus" class="mr-1" />
          新增代理商
        </Button>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-4">
        <Row :gutter="16">
          <Col :span="6">
            <Input
              v-model:value="queryParams.deptName"
              placeholder="代理商名称"
              allow-clear
              @press-enter="handleSearch"
            />
          </Col>
          <Col :span="6">
            <Select
              v-model:value="queryParams.deptType"
              placeholder="部门类型"
              allow-clear
              :options="deptTypeOptions"
            />
          </Col>
          <Col :span="6">
            <Select
              v-model:value="queryParams.enable"
              placeholder="状态"
              allow-clear
              :options="statusOptions"
            />
          </Col>
          <Col :span="6">
            <Space>
              <Button type="primary" @click="handleSearch">
                <Icon icon="lucide:search" class="mr-1" />
                搜索
              </Button>
              <Button @click="handleReset">
                <Icon icon="lucide:refresh-cw" class="mr-1" />
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    </Card>

    <!-- 代理商列表 -->
    <Card>
      <Table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'deptName'">
            <div class="flex items-center">
              <div>
                <div class="font-medium">{{ record.deptName }}</div>
                <div class="text-sm text-gray-500">ID: {{ record.id }}</div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'deptType'">
            <Tag :color="record.deptType === 'OUTER' ? 'blue' : 'green'">
              {{ record.deptType === 'OUTER' ? '外部代理商' : '内部部门' }}
            </Tag>
          </template>

          <template v-else-if="column.key === 'enable'">
            <Tag :color="record.enable ? 'green' : 'red'">
              {{ record.enable ? '启用' : '禁用' }}
            </Tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <Space>
              <Button size="small" type="text" @click="viewAgent(record)">
                查看
              </Button>
              <Button size="small" type="text" @click="editAgent(record)">
                编辑
              </Button>
              <Button
                size="small"
                type="text"
                :danger="record.enable"
                @click="toggleStatus(record)"
              >
                {{ record.enable ? '禁用' : '启用' }}
              </Button>
              <Button size="small" type="text" danger @click="deleteAgent(record)">
                删除
              </Button>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 新增/编辑代理商弹窗 -->
    <Modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑代理商' : '新增代理商'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="代理商名称" name="deptName">
              <Input v-model:value="formData.deptName" placeholder="请输入代理商名称" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="部门类型" name="deptType">
              <Select v-model:value="formData.deptType" :options="deptTypeOptions" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="图标" name="icon">
              <Input v-model:value="formData.icon" placeholder="请输入图标名称" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="状态" name="enable">
              <Select v-model:value="formData.enable" :options="statusOptions" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="排序" name="sort">
              <Input v-model:value="formData.sort" placeholder="请输入排序值" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="顺序" name="order">
              <Input v-model:value="formData.order" placeholder="请输入顺序值" />
            </FormItem>
          </Col>
        </Row>
        <FormItem label="描述" name="description">
          <Input.TextArea v-model:value="formData.description" placeholder="请输入描述信息" :rows="3" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  Card,
  Button,
  Input,
  Select,
  Table,
  Tag,
  Space,
  Row,
  Col,
  Modal,
  Form,
  FormItem,
  message
} from 'ant-design-vue';

import {
  getAgentDeptTree,
  createAgent as createAgentApi,
  updateAgent as updateAgentApi,
  deleteAgent as deleteAgentApi,
  toggleAgentStatus
} from '#/api/agent-management/agent';

// 响应式数据
const loading = ref(false);
const dataSource = ref<any[]>([]);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive({
  deptName: '',
  deptType: undefined,
  enable: undefined,
});

// 状态选项
const statusOptions = [
  { label: '启用', value: 'true' },
  { label: '禁用', value: 'false' },
];

// 部门类型选项
const deptTypeOptions = [
  { label: '外部代理商', value: 'OUTER' },
  { label: '内部部门', value: 'INNER' },
];

// 表格列配置
const columns = [
  {
    title: '代理商名称',
    dataIndex: 'deptName',
    key: 'deptName',
    width: 200,
  },
  {
    title: '部门类型',
    dataIndex: 'deptType',
    key: 'deptType',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80,
  },
  {
    title: '顺序',
    dataIndex: 'order',
    key: 'order',
    width: 80,
  },
  {
    title: '状态',
    dataIndex: 'enable',
    key: 'enable',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
    width: 160,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const,
  },
];

// 表单数据
const formData = reactive({
  id: '',
  deptName: '',
  deptType: 'OUTER',
  description: '',
  icon: 'i-fe:user',
  enable: 'true' as any,
  sort: '1',
  order: '1',
});

// 表单验证规则
const formRules = {
  deptName: [
    { required: true, message: '请输入代理商名称', trigger: 'blur' },
  ],
  deptType: [
    { required: true, message: '请选择部门类型', trigger: 'change' },
  ],
  description: [
    { required: true, message: '请输入描述信息', trigger: 'blur' },
  ],
  icon: [
    { required: true, message: '请输入图标名称', trigger: 'blur' },
  ],
  enable: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
  ],
  order: [
    { required: true, message: '请输入顺序值', trigger: 'blur' },
  ],
} as any;

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getAgentDeptTree(queryParams);

    if (response.code === 'OK') {
      dataSource.value = response.data || [];
    } else {
      message.error(response.msg || '加载数据失败');
    }
  } catch (error) {
    console.error('加载代理商列表失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  loadData();
};

// 重置
const handleReset = () => {
  Object.assign(queryParams, {
    deptName: '',
    deptType: undefined,
    enable: undefined,
  });
  loadData();
};

// 显示新增弹窗
const showAddModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

// 查看代理商
const viewAgent = (record: any) => {
  message.info(`查看代理商: ${record.deptName}`);
};

// 编辑代理商
const editAgent = (record: any) => {
  isEdit.value = true;
  Object.assign(formData, record);
  modalVisible.value = true;
};

// 切换状态
const toggleStatus = (record: any) => {
  const action = record.enable ? '禁用' : '启用';
  Modal.confirm({
    title: `确认${action}`,
    content: `确定要${action}代理商"${record.deptName}"吗？`,
    onOk: async () => {
      try {
        await toggleAgentStatus(record.id, !record.enable);
        record.enable = !record.enable;
        message.success(`${action}成功`);
      } catch (error) {
        message.error(`${action}失败`);
      }
    },
  });
};

// 删除代理商
const deleteAgent = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除代理商"${record.deptName}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await deleteAgentApi(record.id);
        loadData(); // 重新加载数据
        message.success('删除成功');
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    if (isEdit.value) {
      await updateAgentApi(formData);
    } else {
      await createAgentApi(formData);
    }

    message.success(isEdit.value ? '编辑成功' : '新增成功');
    modalVisible.value = false;
    loadData();
  } catch (error) {
    console.error('表单验证失败:', error);
    message.error(isEdit.value ? '编辑失败' : '新增失败');
  }
};

// 取消
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    deptName: '',
    deptType: 'OUTER',
    description: '',
    icon: 'i-fe:user',
    enable: 'true' as any,
    sort: '1',
    order: '1',
  });
  formRef.value?.resetFields();
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.agent-list-page {
  padding: 24px;
}
</style>
