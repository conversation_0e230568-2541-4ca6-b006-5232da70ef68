<template>
  <div class="member-list-page">
    <!-- 页面头部 -->
    <Card class="mb-4">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-2xl font-bold text-gray-800">成员列表</h1>
          <p class="text-gray-600 mt-1">管理代理商成员信息</p>
        </div>
        <Button type="primary" @click="showAddModal">
          <Icon icon="lucide:user-plus" class="mr-1" />
          新增成员
        </Button>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-4">
        <Row :gutter="16">
          <Col :span="5">
            <Select
              v-model:value="queryParams.agentId"
              placeholder="选择代理商"
              allow-clear
              :options="agentOptions"
              @change="handleSearch"
            />
          </Col>
          <Col :span="5">
            <Input
              v-model:value="queryParams.userName"
              placeholder="用户名/姓名"
              allow-clear
              @press-enter="handleSearch"
            />
          </Col>
          <Col :span="5">
            <Input
              v-model:value="queryParams.phone"
              placeholder="手机号"
              allow-clear
              @press-enter="handleSearch"
            />
          </Col>
          <Col :span="4">
            <Select
              v-model:value="queryParams.status"
              placeholder="状态"
              allow-clear
              :options="statusOptions"
            />
          </Col>
          <Col :span="5">
            <Space>
              <Button type="primary" @click="handleSearch">
                <Icon icon="lucide:search" class="mr-1" />
                搜索
              </Button>
              <Button @click="handleReset">
                <Icon icon="lucide:refresh-cw" class="mr-1" />
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    </Card>

    <!-- 成员列表 -->
    <Card>
      <Table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="userId"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userInfo'">
            <div class="flex items-center">
              <Avatar :size="40" :src="record.avatar">
                <template #icon>
                  <Icon icon="lucide:user" />
                </template>
              </Avatar>
              <div class="ml-3">
                <div class="font-medium">{{ record.realName }}</div>
                <div class="text-sm text-gray-500">{{ record.userName }}</div>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'agentName'">
            <div class="flex items-center">
              <Icon icon="lucide:building" class="mr-2 text-blue-500" />
              <span>{{ record.agentName }}</span>
            </div>
          </template>

          <template v-else-if="column.key === 'role'">
            <Tag :color="getRoleColor(record.role)">
              {{ getRoleText(record.role) }}
            </Tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <Tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </Tag>
          </template>

          <template v-else-if="column.key === 'lastLoginTime'">
            <div v-if="record.lastLoginTime">
              <div>{{ record.lastLoginTime }}</div>
              <div class="text-sm text-gray-500">{{ getTimeAgo(record.lastLoginTime) }}</div>
            </div>
            <span v-else class="text-gray-400">从未登录</span>
          </template>

          <template v-else-if="column.key === 'action'">
            <Space>
              <Button size="small" type="text" @click="viewMember(record)">
                <Icon icon="lucide:eye" />
              </Button>
              <Button size="small" type="text" @click="editMember(record)">
                <Icon icon="lucide:edit" />
              </Button>
              <Button size="small" type="text" @click="resetPassword(record)">
                <Icon icon="lucide:key" />
              </Button>
              <Button 
                size="small" 
                type="text" 
                :danger="record.status === 1"
                @click="toggleStatus(record)"
              >
                <Icon :icon="record.status === 1 ? 'lucide:ban' : 'lucide:check'" />
              </Button>
              <Button size="small" type="text" danger @click="deleteMember(record)">
                <Icon icon="lucide:trash-2" />
              </Button>
            </Space>
          </template>
        </template>
      </Table>
    </Card>

    <!-- 新增/编辑成员弹窗 -->
    <Modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑成员' : '新增成员'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <Form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="所属代理商" name="agentId">
              <Select 
                v-model:value="formData.agentId" 
                placeholder="请选择代理商"
                :options="agentOptions"
              />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="用户名" name="userName">
              <Input 
                v-model:value="formData.userName" 
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="真实姓名" name="realName">
              <Input v-model:value="formData.realName" placeholder="请输入真实姓名" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="手机号" name="phone">
              <Input v-model:value="formData.phone" placeholder="请输入手机号" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="邮箱" name="email">
              <Input v-model:value="formData.email" placeholder="请输入邮箱地址" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="角色" name="role">
              <Select v-model:value="formData.role" :options="roleOptions" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16" v-if="!isEdit">
          <Col :span="12">
            <FormItem label="密码" name="password">
              <Input.Password v-model:value="formData.password" placeholder="请输入密码" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="确认密码" name="confirmPassword">
              <Input.Password v-model:value="formData.confirmPassword" placeholder="请确认密码" />
            </FormItem>
          </Col>
        </Row>
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="状态" name="status">
              <Select v-model:value="formData.status" :options="statusOptions" />
            </FormItem>
          </Col>
        </Row>
        <FormItem label="备注" name="remark">
          <Input.TextArea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="3" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  Card,
  Button,
  Input,
  Select,
  Table,
  Tag,
  Space,
  Row,
  Col,
  Modal,
  Form,
  FormItem,
  Avatar,
  message
} from 'ant-design-vue';
import { Icon } from '@iconify/vue';

// 响应式数据
const loading = ref(false);
const dataSource = ref([]);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive({
  agentId: undefined,
  userName: '',
  phone: '',
  status: undefined,
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 代理商选项
const agentOptions = ref([
  { label: '华南教育科技有限公司', value: 'AGENT001' },
  { label: '北京智慧教育集团', value: 'AGENT002' },
]);

// 状态选项
const statusOptions = [
  { label: '正常', value: 1 },
  { label: '禁用', value: 0 },
];

// 角色选项
const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '普通成员', value: 'member' },
  { label: '客服', value: 'service' },
];

// 表格列配置
const columns = [
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    key: 'userInfo',
    width: 200,
  },
  {
    title: '所属代理商',
    dataIndex: 'agentName',
    key: 'agentName',
    width: 180,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 130,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180,
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginTime',
    key: 'lastLoginTime',
    width: 160,
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
  },
];

// 表单数据
const formData = reactive({
  userId: '',
  agentId: undefined,
  userName: '',
  realName: '',
  phone: '',
  email: '',
  role: 'member',
  status: 1,
  password: '',
  confirmPassword: '',
  remark: '',
});

// 表单验证规则
const formRules = {
  agentId: [
    { required: true, message: '请选择代理商', trigger: 'change' },
  ],
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' },
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== formData.password) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur',
    },
  ],
};

// 获取角色颜色
const getRoleColor = (role: string) => {
  const colorMap: Record<string, string> = {
    admin: 'red',
    member: 'blue',
    service: 'green',
  };
  return colorMap[role] || 'default';
};

// 获取角色文本
const getRoleText = (role: string) => {
  const textMap: Record<string, string> = {
    admin: '管理员',
    member: '普通成员',
    service: '客服',
  };
  return textMap[role] || role;
};

// 获取时间差
const getTimeAgo = (time: string) => {
  const now = new Date();
  const loginTime = new Date(time);
  const diff = now.getTime() - loginTime.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天';
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return `${Math.floor(days / 7)}周前`;
  }
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // TODO: 调用真实API
    // const response = await getMemberList({
    //   pageNum: pagination.current,
    //   pageSize: pagination.pageSize,
    //   ...queryParams,
    // });
    
    // 模拟数据
    const mockData = [
      {
        userId: 'USER001',
        agentId: 'AGENT001',
        agentName: '华南教育科技有限公司',
        userName: 'zhangsan',
        realName: '张三',
        phone: '13800138001',
        email: '<EMAIL>',
        role: 'admin',
        status: 1,
        avatar: '',
        lastLoginTime: '2024-01-25 14:30:00',
        createTime: '2024-01-15 10:30:00',
        remark: '代理商管理员',
      },
      {
        userId: 'USER002',
        agentId: 'AGENT001',
        agentName: '华南教育科技有限公司',
        userName: 'lisi',
        realName: '李四',
        phone: '13800138002',
        email: '<EMAIL>',
        role: 'member',
        status: 1,
        avatar: '',
        lastLoginTime: '2024-01-24 16:20:00',
        createTime: '2024-01-16 11:20:00',
        remark: '普通成员',
      },
      {
        userId: 'USER003',
        agentId: 'AGENT002',
        agentName: '北京智慧教育集团',
        userName: 'wangwu',
        realName: '王五',
        phone: '13800138003',
        email: '<EMAIL>',
        role: 'service',
        status: 1,
        avatar: '',
        lastLoginTime: null,
        createTime: '2024-01-20 15:10:00',
        remark: '客服人员',
      },
    ];
    
    dataSource.value = mockData;
    pagination.total = mockData.length;
  } catch (error) {
    console.error('加载成员列表失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置
const handleReset = () => {
  Object.assign(queryParams, {
    agentId: undefined,
    userName: '',
    phone: '',
    status: undefined,
  });
  pagination.current = 1;
  loadData();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

// 显示新增弹窗
const showAddModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

// 查看成员
const viewMember = (record: any) => {
  message.info(`查看成员: ${record.realName}`);
};

// 编辑成员
const editMember = (record: any) => {
  isEdit.value = true;
  Object.assign(formData, record);
  modalVisible.value = true;
};

// 重置密码
const resetPassword = (record: any) => {
  Modal.confirm({
    title: '重置密码',
    content: `确定要重置用户"${record.realName}"的密码吗？新密码将发送到用户手机。`,
    onOk: async () => {
      try {
        // TODO: 调用真实API
        message.success('密码重置成功，新密码已发送到用户手机');
      } catch (error) {
        message.error('密码重置失败');
      }
    },
  });
};

// 切换状态
const toggleStatus = (record: any) => {
  const action = record.status === 1 ? '禁用' : '启用';
  Modal.confirm({
    title: `确认${action}`,
    content: `确定要${action}用户"${record.realName}"吗？`,
    onOk: async () => {
      try {
        // TODO: 调用真实API
        record.status = record.status === 1 ? 0 : 1;
        message.success(`${action}成功`);
      } catch (error) {
        message.error(`${action}失败`);
      }
    },
  });
};

// 删除成员
const deleteMember = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除成员"${record.realName}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        // TODO: 调用真实API
        const index = dataSource.value.findIndex((item: any) => item.userId === record.userId);
        if (index > -1) {
          dataSource.value.splice(index, 1);
        }
        message.success('删除成功');
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    // TODO: 调用真实API
    message.success(isEdit.value ? '编辑成功' : '新增成功');
    modalVisible.value = false;
    loadData();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 取消
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    userId: '',
    agentId: undefined,
    userName: '',
    realName: '',
    phone: '',
    email: '',
    role: 'member',
    status: 1,
    password: '',
    confirmPassword: '',
    remark: '',
  });
  formRef.value?.resetFields();
};

// 初始化
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.member-list-page {
  padding: 24px;
}
</style>
