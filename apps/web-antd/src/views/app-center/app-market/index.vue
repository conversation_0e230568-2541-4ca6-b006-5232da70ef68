<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Select,
  Rate,
  Tag,
  Badge,
  message,
  Modal,
  Pagination,
  Spin,
  Image,
} from 'ant-design-vue';
import {
  getMarketAppPage,
  installApp,
  uninstallApp,
  getMarketAppDetail,
  type MarketApp,
  type MarketAppQueryParams,
} from '#/api/app-center/app-market';
import { getAllAppGroups, type AppGroup } from '#/api/app-center/app-group';

defineOptions({
  name: 'AppMarket',
});

// 响应式数据
const loading = ref(false);
const groupLoading = ref(false);
const installLoading = ref<Record<string, boolean>>({});
const detailModalVisible = ref(false);
const selectedApp = ref<MarketApp | null>(null);
const dataSource = ref<MarketApp[]>([]);
const total = ref(0);
const appGroups = ref<AppGroup[]>([]);
const selectedGroupId = ref<string | undefined>(undefined);

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 12,
});

// 查询参数
const queryParams = reactive<Partial<MarketAppQueryParams>>({
  clientName: '',
  clientGroupId: undefined,
});

// 加载应用组数据
const loadAppGroups = async () => {
  try {
    groupLoading.value = true;
    const response = await getAllAppGroups();
    console.log('应用组API响应:', response);

    // 检查多种可能的成功状态码
    const isSuccess = response.code === 'OK' ||
                     response.code === '200' ||
                     response.code === 200 ||
                     response.code === '0' ||
                     response.code === 0;

    if (isSuccess) {
      appGroups.value = response.data || [];
    } else {
      console.error('应用组API返回错误:', response);
      message.error(response.msg || '加载应用组失败');
    }
  } catch (error) {
    console.error('加载应用组失败:', error);
    message.error('加载应用组失败');
  } finally {
    groupLoading.value = false;
  }
};

// 数据加载方法
const loadData = async () => {
  try {
    loading.value = true;
    const params: MarketAppQueryParams = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...queryParams,
    };
    
    const response = await getMarketAppPage(params);
    console.log('应用市场API响应:', response);

    // 检查多种可能的成功状态码
    const isSuccess = response.code === 'OK' ||
                     response.code === '200' ||
                     response.code === 200 ||
                     response.code === '0' ||
                     response.code === 0;

    if (isSuccess) {
      dataSource.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      console.error('应用市场API返回错误:', response);
      message.error(response.msg || '加载应用市场数据失败');
    }
  } catch (error) {
    console.error('加载应用市场数据失败:', error);
    message.error('加载应用市场数据失败');
  } finally {
    loading.value = false;
  }
};

// 应用组选择处理
const handleGroupSelect = (groupId: string | undefined) => {
  selectedGroupId.value = groupId;
  queryParams.clientGroupId = groupId;
  pagination.pageNum = 1;
  loadData();
};

// 搜索处理
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  selectedGroupId.value = undefined;
  Object.assign(queryParams, {
    clientName: '',
    clientGroupId: undefined,
  });
  pagination.pageNum = 1;
  loadData();
};

// 分页处理
const handlePageChange = (page: number, pageSize: number) => {
  pagination.pageNum = page;
  pagination.pageSize = pageSize;
  loadData();
};

// 安装应用
const handleInstall = async (app: MarketApp) => {
  try {
    installLoading.value[app.clientId] = true;
    await installApp(app.clientId);
    message.success(`${app.clientName} 安装成功`);

    // 更新本地状态
    const index = dataSource.value.findIndex(item => item.clientId === app.clientId);
    if (index !== -1) {
      dataSource.value[index].isInstalled = true;
      dataSource.value[index].installTime = new Date().toLocaleString();
    }
  } catch (error) {
    console.error('安装失败:', error);
    message.error('安装失败');
  } finally {
    installLoading.value[app.clientId] = false;
  }
};

// 卸载应用
const handleUninstall = async (app: MarketApp) => {
  try {
    installLoading.value[app.clientId] = true;
    await uninstallApp(app.clientId);
    message.success(`${app.clientName} 卸载成功`);

    // 更新本地状态
    const index = dataSource.value.findIndex(item => item.clientId === app.clientId);
    if (index !== -1) {
      dataSource.value[index].isInstalled = false;
      dataSource.value[index].installTime = undefined;
    }
  } catch (error) {
    console.error('卸载失败:', error);
    message.error('卸载失败');
  } finally {
    installLoading.value[app.clientId] = false;
  }
};

// 查看应用详情
const showAppDetail = async (app: MarketApp) => {
  try {
    const response = await getMarketAppDetail(app.clientId);
    console.log('应用详情API响应:', response);

    // 检查多种可能的成功状态码
    const isSuccess = response.code === 'OK' ||
                     response.code === '200' ||
                     response.code === 200 ||
                     response.code === '0' ||
                     response.code === 0;

    if (isSuccess) {
      selectedApp.value = response.data;
      detailModalVisible.value = true;
    } else {
      console.error('应用详情API返回错误:', response);
      message.error(response.msg || '获取应用详情失败');
    }
  } catch (error) {
    console.error('获取应用详情失败:', error);
    message.error('获取应用详情失败');
  }
};

// 关闭详情弹窗
const closeDetailModal = () => {
  detailModalVisible.value = false;
  selectedApp.value = null;
};

// 格式化下载数量
const formatDownloadCount = (count: number) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`;
  }
  return count.toString();
};

// 获取分类标签颜色
const getCategoryColor = (groupId: string | undefined) => {
  if (!groupId) return 'default';

  // 根据应用组ID生成颜色
  const colors = ['blue', 'green', 'purple', 'orange', 'cyan', 'red', 'pink', 'yellow'];
  const index = groupId.length % colors.length;
  return colors[index];
};

// 检查是否为有效的图片URL
const isValidImageUrl = (url: string) => {
  if (!url) return false;
  return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:image/');
};

// 获取图标占位符
const getIconPlaceholder = (name: string) => {
  if (!name) return '?';
  return /[\u4e00-\u9fa5]/.test(name) ? name.charAt(0) : name.charAt(0).toUpperCase();
};

// 组件挂载时加载数据
onMounted(() => {
  loadAppGroups();
  loadData();
});
</script>

<template>
  <div class="app-market-page">
    <!-- 页面头部 -->
    <Card class="mb-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold mb-2">应用市场</h1>
          <p class="text-gray-600">浏览和安装应用市场中的应用</p>
        </div>
      </div>
    </Card>

    <!-- 应用组导航 -->
    <Card class="mb-4">
      <div class="flex items-center justify-between mb-4">
        <Input.Search
          v-model:value="queryParams.clientName"
          placeholder="搜索应用名称"
          allow-clear
          style="width: 300px"
          @search="handleSearch"
        />
        <h2 class="text-lg font-medium">应用分类</h2>
      </div>

      <div class="flex flex-wrap gap-2">
        <!-- 全部应用 -->
        <Button
          :type="selectedGroupId === undefined ? 'primary' : 'default'"
          @click="handleGroupSelect(undefined)"
        >
          全部应用
        </Button>

        <!-- 应用组列表 -->
        <Button
          v-for="group in appGroups"
          :key="group.clientGroupId"
          :type="selectedGroupId === group.clientGroupId ? 'primary' : 'default'"
          @click="handleGroupSelect(group.clientGroupId)"
        >
          {{ group.clientGroupName }}
        </Button>
      </div>
    </Card>

    <!-- 应用列表 -->
    <Spin :spinning="loading">
      <Row :gutter="[16, 16]">
        <Col
          v-for="app in dataSource"
          :key="app.clientId"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          :xl="6"
        >
          <Card
            hoverable
            class="app-card h-full"
            :body-style="{ padding: '16px' }"
          >
            <div class="flex flex-col h-full">
              <!-- 应用图标和基本信息 -->
              <div class="flex items-start space-x-3 mb-3">
                <div class="w-12 h-12 rounded-lg overflow-hidden border border-gray-200 bg-gray-50 flex items-center justify-center flex-shrink-0">
                  <img
                    v-if="app.clientIcon && isValidImageUrl(app.clientIcon)"
                    :src="app.clientIcon"
                    :alt="app.clientName"
                    class="w-full h-full object-cover"
                  />
                  <div
                    v-else
                    class="w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-sm font-medium"
                  >
                    {{ getIconPlaceholder(app.clientName) }}
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-medium text-gray-900 truncate">{{ app.clientName }}</h3>
                  <p class="text-sm text-gray-500">{{ app.developer }}</p>
                  <div class="flex items-center space-x-2 mt-1">
                    <Rate :value="app.rating" disabled allow-half class="text-xs" />
                    <span class="text-xs text-gray-500">{{ app.rating }}</span>
                  </div>
                </div>
              </div>

              <!-- 应用描述 -->
              <p class="text-sm text-gray-600 mb-3 flex-1 line-clamp-2">
                {{ app.clientDesc }}
              </p>

              <!-- 标签和信息 -->
              <div class="mb-3">
                <div class="flex items-center justify-between mb-2">
                  <Tag :color="getCategoryColor(app.clientGroupId)" size="small">
                    {{ app.clientGroupName || '未分组' }}
                  </Tag>
                  <span class="text-xs text-gray-500">{{ app.clientType === 0 ? '自有应用' : '第三方应用' }}</span>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>{{ formatDownloadCount(app.downloadCount || 0) }} 下载</span>
                  <span v-if="(app.price || 0) === 0" class="text-green-600 font-medium">免费</span>
                  <span v-else class="text-orange-600 font-medium">¥{{ app.price || 0 }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex space-x-2">
                <Button
                  v-if="!app.isInstalled"
                  type="primary"
                  size="small"
                  :loading="installLoading[app.clientId]"
                  @click="handleInstall(app)"
                  class="flex-1"
                >
                  {{ app.price === 0 ? '安装' : '购买' }}
                </Button>
                <Button
                  v-else
                  size="small"
                  :loading="installLoading[app.clientId]"
                  @click="handleUninstall(app)"
                  class="flex-1"
                >
                  卸载
                </Button>
                <Button
                  size="small"
                  @click="showAppDetail(app)"
                >
                  详情
                </Button>
              </div>

              <!-- 已安装标识 -->
              <Badge
                v-if="app.isInstalled"
                status="success"
                text="已安装"
                class="absolute top-2 right-2"
              />
            </div>
          </Card>
        </Col>
      </Row>

      <!-- 空状态 -->
      <div v-if="!loading && dataSource.length === 0" class="text-center py-12">
        <div class="text-gray-400 text-lg mb-2">📱</div>
        <p class="text-gray-500">暂无应用</p>
      </div>
    </Spin>

    <!-- 分页 -->
    <div class="flex justify-center mt-6" v-if="total > 0">
      <Pagination
        v-model:current="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageChange"
      />
    </div>

    <!-- 应用详情弹窗 -->
    <Modal
      v-model:open="detailModalVisible"
      :title="selectedApp?.clientName"
      width="800px"
      :footer="null"
      @cancel="closeDetailModal"
    >
      <div v-if="selectedApp" class="app-detail">
        <!-- 应用基本信息 -->
        <div class="flex items-start space-x-4 mb-6">
          <div class="w-16 h-16 rounded-lg overflow-hidden border border-gray-200 bg-gray-50 flex items-center justify-center flex-shrink-0">
            <img
              v-if="selectedApp.clientIcon && isValidImageUrl(selectedApp.clientIcon)"
              :src="selectedApp.clientIcon"
              :alt="selectedApp.clientName"
              class="w-full h-full object-cover"
            />
            <div
              v-else
              class="w-full h-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white font-medium"
            >
              {{ getIconPlaceholder(selectedApp.clientName) }}
            </div>
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-bold mb-2">{{ selectedApp.clientName }}</h2>
            <p class="text-gray-600 mb-2">{{ selectedApp.developer }}</p>
            <div class="flex items-center space-x-4 mb-2">
              <div class="flex items-center space-x-1">
                <Rate :value="selectedApp.rating" disabled allow-half />
                <span class="text-sm text-gray-500">{{ selectedApp.rating }}</span>
              </div>
              <span class="text-sm text-gray-500">{{ formatDownloadCount(selectedApp.downloadCount || 0) }} 下载</span>
              <Tag :color="getCategoryColor(selectedApp.clientGroupId)">
                {{ selectedApp.clientGroupName }}
              </Tag>
            </div>
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-500">类型: {{ selectedApp.clientType === 0 ? '自有应用' : '第三方应用' }}</span>
              <span v-if="selectedApp.price === 0" class="text-green-600 font-medium">免费</span>
              <span v-else class="text-orange-600 font-medium text-lg">¥{{ selectedApp.price }}</span>
            </div>
          </div>
        </div>

        <!-- 应用描述 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-2">应用介绍</h3>
          <p class="text-gray-600 leading-relaxed">{{ selectedApp.clientDetail || selectedApp.clientDesc }}</p>
        </div>

        <!-- 应用特性 -->
        <div v-if="selectedApp.features && selectedApp.features.length > 0" class="mb-6">
          <h3 class="text-lg font-medium mb-2">主要特性</h3>
          <div class="grid grid-cols-2 gap-2">
            <div
              v-for="feature in selectedApp.features"
              :key="feature"
              class="flex items-center space-x-2"
            >
              <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span class="text-sm text-gray-600">{{ feature }}</span>
            </div>
          </div>
        </div>

        <!-- 应用截图 -->
        <div v-if="selectedApp.screenshots && selectedApp.screenshots.length > 0" class="mb-6">
          <h3 class="text-lg font-medium mb-2">应用截图</h3>
          <div class="grid grid-cols-3 gap-2">
            <Image
              v-for="(screenshot, index) in selectedApp.screenshots"
              :key="index"
              :src="screenshot"
              :alt="`截图 ${index + 1}`"
              class="rounded-lg"
              :preview="{ mask: '预览' }"
            />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-2">
          <Button @click="closeDetailModal">关闭</Button>
          <Button
            v-if="!selectedApp.isInstalled"
            type="primary"
            :loading="installLoading[selectedApp.clientId]"
            @click="handleInstall(selectedApp)"
          >
            {{ selectedApp.price === 0 ? '安装' : '购买' }}
          </Button>
          <Button
            v-else
            :loading="installLoading[selectedApp.clientId]"
            @click="handleUninstall(selectedApp)"
          >
            卸载
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style lang="scss" scoped>
.app-market-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.app-card {
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.app-detail {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.ant-rate) {
  font-size: 12px;
}

:deep(.ant-badge) {
  .ant-badge-status-dot {
    width: 8px;
    height: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-market-page {
    padding: 16px;
  }
}
</style>
