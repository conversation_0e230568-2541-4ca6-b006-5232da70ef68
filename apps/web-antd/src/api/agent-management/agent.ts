/**
 * 代理商管理 API
 */

import { requestClient } from '#/api/request';

/**
 * 代理商数据类型定义
 */
export interface Agent {
  agentId: string;
  agentName: string;
  contactName: string;
  contactPhone: string;
  email?: string;
  address?: string;
  memberCount?: number;
  status: number; // 0: 禁用, 1: 启用
  createTime: string;
  updateTime?: string;
  remark?: string;
}

/**
 * 代理商查询参数
 */
export interface AgentQueryParams {
  pageNum: number;
  pageSize: number;
  agentName?: string;
  contactPhone?: string;
  status?: number;
}

/**
 * 代理商表单数据
 */
export interface AgentFormData {
  agentId?: string;
  agentName: string;
  contactName: string;
  contactPhone: string;
  email?: string;
  address?: string;
  status: number;
  remark?: string;
}

/**
 * 分页响应数据
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/**
 * API响应数据
 */
export interface ApiResponse<T = any> {
  code: string | number;
  message: string;
  data: T;
}

/**
 * 获取代理商分页列表
 */
export async function getAgentPage(params: AgentQueryParams): Promise<ApiResponse<PageResult<Agent>>> {
  return requestClient.get('/api/v3/agent/v1/agent/page', {
    params,
  });
}

/**
 * 根据ID获取代理商详情
 */
export async function getAgentById(agentId: string): Promise<ApiResponse<Agent>> {
  return requestClient.get(`/api/v3/agent/v1/agent/getById`, {
    params: { agentId },
  });
}

/**
 * 创建代理商
 */
export async function createAgent(data: AgentFormData): Promise<ApiResponse<Agent>> {
  return requestClient.post('/api/v3/agent/v1/agent/create', data);
}

/**
 * 更新代理商
 */
export async function updateAgent(data: AgentFormData): Promise<ApiResponse<Agent>> {
  return requestClient.post('/api/v3/agent/v1/agent/update', data);
}

/**
 * 删除代理商
 */
export async function deleteAgent(agentId: string): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/agent/delete', {
    agentId,
  });
}

/**
 * 切换代理商状态
 */
export async function toggleAgentStatus(agentId: string, status: number): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/agent/toggleStatus', {
    agentId,
    status,
  });
}

/**
 * 获取代理商选项列表（用于下拉选择）
 */
export async function getAgentOptions(): Promise<ApiResponse<Array<{ label: string; value: string }>>> {
  return requestClient.get('/api/v3/agent/v1/agent/options');
}
