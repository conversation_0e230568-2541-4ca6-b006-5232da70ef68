/**
 * 代理商管理 API
 */

import { requestClient } from '#/api/request';

/**
 * 代理商数据类型定义（基于真实API返回结构）
 */
export interface Agent {
  id: string;
  createdTime: string | null;
  updatedTime: string | null;
  deptName: string;
  deptType: string; // "OUTER" 等
  description: string;
  icon: string;
  show: boolean | null;
  enable: boolean;
  sort: string;
  order: string;
}

/**
 * 代理商查询参数
 */
export interface AgentQueryParams {
  deptName?: string;
  deptType?: string;
  enable?: boolean;
}

/**
 * 代理商表单数据
 */
export interface AgentFormData {
  id?: string;
  deptName: string;
  deptType: string;
  description: string;
  icon: string;
  enable: boolean;
  sort: string;
  order: string;
}

/**
 * API响应数据（基于真实API返回结构）
 */
export interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
  traceId: string | null;
  spanId: string | null;
}

/**
 * 获取代理商树形列表（基于真实API）
 */
export async function getAgentDeptTree(params?: AgentQueryParams): Promise<ApiResponse<Agent[]>> {
  return requestClient.get('/api/v3/agent/dept/deptTree', {
    params,
  });
}

/**
 * 根据ID获取代理商详情
 */
export async function getAgentById(id: string): Promise<ApiResponse<Agent>> {
  return requestClient.get(`/api/v3/agent/dept/getById`, {
    params: { id },
  });
}

/**
 * 创建代理商
 */
export async function createAgent(data: AgentFormData): Promise<ApiResponse<Agent>> {
  return requestClient.post('/api/v3/agent/dept/create', data);
}

/**
 * 更新代理商
 */
export async function updateAgent(data: AgentFormData): Promise<ApiResponse<Agent>> {
  return requestClient.post('/api/v3/agent/dept/update', data);
}

/**
 * 删除代理商
 */
export async function deleteAgent(id: string): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/dept/delete', {
    id,
  });
}

/**
 * 切换代理商状态
 */
export async function toggleAgentStatus(id: string, enable: boolean): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/dept/toggleStatus', {
    id,
    enable,
  });
}

/**
 * 获取代理商选项列表（用于下拉选择）
 */
export async function getAgentOptions(): Promise<ApiResponse<Array<{ label: string; value: string }>>> {
  return requestClient.get('/api/v3/agent/dept/options');
}
