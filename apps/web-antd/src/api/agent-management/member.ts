/**
 * 代理商成员管理 API
 */

import { requestClient } from '#/api/request';

/**
 * 成员数据类型定义
 */
export interface Member {
  userId: string;
  agentId: string;
  agentName: string;
  userName: string;
  realName: string;
  phone: string;
  email?: string;
  avatar?: string;
  role: string; // admin: 管理员, member: 普通成员, service: 客服
  status: number; // 0: 禁用, 1: 正常
  lastLoginTime?: string;
  createTime: string;
  updateTime?: string;
  remark?: string;
}

/**
 * 成员查询参数
 */
export interface MemberQueryParams {
  pageNum: number;
  pageSize: number;
  agentId?: string;
  userName?: string;
  phone?: string;
  role?: string;
  status?: number;
}

/**
 * 成员表单数据
 */
export interface MemberFormData {
  userId?: string;
  agentId: string;
  userName: string;
  realName: string;
  phone: string;
  email?: string;
  role: string;
  status: number;
  password?: string;
  confirmPassword?: string;
  remark?: string;
}

/**
 * 分页响应数据
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/**
 * API响应数据
 */
export interface ApiResponse<T = any> {
  code: string | number;
  message: string;
  data: T;
}

/**
 * 获取成员分页列表
 */
export async function getMemberPage(params: MemberQueryParams): Promise<ApiResponse<PageResult<Member>>> {
  return requestClient.get('/api/v3/agent/v1/member/page', {
    params,
  });
}

/**
 * 根据ID获取成员详情
 */
export async function getMemberById(userId: string): Promise<ApiResponse<Member>> {
  return requestClient.get(`/api/v3/agent/v1/member/getById`, {
    params: { userId },
  });
}

/**
 * 根据代理商ID获取成员列表
 */
export async function getMembersByAgentId(agentId: string): Promise<ApiResponse<Member[]>> {
  return requestClient.get('/api/v3/agent/v1/member/listByAgent', {
    params: { agentId },
  });
}

/**
 * 创建成员
 */
export async function createMember(data: MemberFormData): Promise<ApiResponse<Member>> {
  return requestClient.post('/api/v3/agent/v1/member/create', data);
}

/**
 * 更新成员
 */
export async function updateMember(data: MemberFormData): Promise<ApiResponse<Member>> {
  return requestClient.post('/api/v3/agent/v1/member/update', data);
}

/**
 * 删除成员
 */
export async function deleteMember(userId: string): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/member/delete', {
    userId,
  });
}

/**
 * 切换成员状态
 */
export async function toggleMemberStatus(userId: string, status: number): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/member/toggleStatus', {
    userId,
    status,
  });
}

/**
 * 重置成员密码
 */
export async function resetMemberPassword(userId: string): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/member/resetPassword', {
    userId,
  });
}

/**
 * 批量删除成员
 */
export async function batchDeleteMembers(userIds: string[]): Promise<ApiResponse<void>> {
  return requestClient.post('/api/v3/agent/v1/member/batchDelete', {
    userIds,
  });
}

/**
 * 导出成员列表
 */
export async function exportMembers(params: MemberQueryParams): Promise<Blob> {
  return requestClient.get('/api/v3/agent/v1/member/export', {
    params,
    responseType: 'blob',
  });
}
