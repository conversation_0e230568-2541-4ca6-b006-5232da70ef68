/**
 * 应用市场 API - 对接app-template接口，只展示已上架的应用
 */

import { requestClient } from '#/api/request';

/**
 * 市场应用数据类型定义 - 基于 ClientDTO 但适配市场展示
 */
export interface MarketApp {
  clientId: string;
  clientName: string;
  clientIcon?: string;
  clientDesc?: string;
  clientDetail?: string;
  clientGroupId?: string;
  clientGroupName?: string;
  clientType?: number; // 0自有应用，1第三方应用
  clientStatus?: number; // 0上架，1下架
  systemApp?: number; // 0非系统应用，1系统应用
  // 市场特有字段（可能需要从其他接口获取或计算）
  rating?: number;
  downloadCount?: number;
  price?: number; // 0表示免费
  screenshots?: string[];
  features?: string[];
  isInstalled?: boolean;
  installTime?: string;
  developer?: string;
}

/**
 * 应用市场查询参数 - 基于 ClientPageQry 但只查询已上架应用
 */
export interface MarketAppQueryParams {
  pageNum: number;
  pageSize: number;
  pageIndex?: number;
  sqlFilter?: string;
  params?: Record<string, any>;
  clientGroupId?: string;
  clientName?: string;
}

/**
 * 分页响应数据
 */
export interface MarketAppPageResult {
  total: number;
  records: MarketApp[];
}

/**
 * API响应格式
 */
export interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
  链路ID?: string;
  标签ID?: string;
}

/**
 * 应用市场相关接口 - 对接真实API
 */

/**
 * 分页查询市场应用（只展示已上架的应用）
 */
export async function getMarketAppPage(params: MarketAppQueryParams): Promise<ApiResponse<MarketAppPageResult>> {
  // 构建查询参数，确保只查询已上架的应用
  const queryParams = {
    ...params,
    // 添加筛选条件：只查询已上架的应用 (clientStatus = 0)
    sqlFilter: params.sqlFilter ? `${params.sqlFilter} AND clientStatus = 0` : 'clientStatus = 0',
  };

  const response = await requestClient.post('/api/v3/agent/v1/client/page', queryParams);

  // 转换数据格式以适配市场展示
  if (response.code === 'OK' && response.data) {
    const marketApps: MarketApp[] = response.data.records.map((app: any) => ({
      clientId: app.clientId,
      clientName: app.clientName,
      clientIcon: app.clientIcon,
      clientDesc: app.clientDesc,
      clientDetail: app.clientDetail,
      clientGroupId: app.clientGroupId,
      clientGroupName: app.clientGroupName,
      clientType: app.clientType,
      clientStatus: app.clientStatus,
      systemApp: app.systemApp,
      // 市场特有字段，可能需要从其他接口获取或设置默认值
      rating: 4.5, // 默认评分
      downloadCount: Math.floor(Math.random() * 10000), // 模拟下载量
      price: 0, // 默认免费
      screenshots: [],
      features: [],
      isInstalled: false,
      developer: '官方',
    }));

    return {
      ...response,
      data: {
        total: response.data.total,
        records: marketApps,
      },
    };
  }

  return response;
}
/**
 * 获取应用详情（市场版本）
 */
export async function getMarketAppDetail(clientId: string): Promise<ApiResponse<MarketApp>> {
  const params = { clientId };
  const response = await requestClient.post('/api/v3/agent/v1/client/getById', params);

  // 转换数据格式以适配市场展示
  if (response.code === 'OK' && response.data) {
    const app = response.data;
    const marketApp: MarketApp = {
      clientId: app.clientId,
      clientName: app.clientName,
      clientIcon: app.clientIcon,
      clientDesc: app.clientDesc,
      clientDetail: app.clientDetail,
      clientGroupId: app.clientGroupId,
      clientGroupName: app.clientGroupName,
      clientType: app.clientType,
      clientStatus: app.clientStatus,
      systemApp: app.systemApp,
      // 市场特有字段
      rating: 4.5,
      downloadCount: Math.floor(Math.random() * 10000),
      price: 0,
      screenshots: [],
      features: [],
      isInstalled: false,
      developer: '官方',
    };

    return {
      ...response,
      data: marketApp,
    };
  }

  return response;
}
/**
 * 安装应用（简化版本，实际可能需要调用学校应用管理接口）
 */
export async function installApp(clientId: string): Promise<ApiResponse<void>> {
  // 这里应该调用学校应用管理的安装接口
  // 暂时返回成功响应
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 'OK',
        msg: '安装成功',
        data: undefined,
      });
    }, 2000);
  });
}

/**
 * 卸载应用（简化版本，实际可能需要调用学校应用管理接口）
 */
export async function uninstallApp(clientId: string): Promise<ApiResponse<void>> {
  // 这里应该调用学校应用管理的卸载接口
  // 暂时返回成功响应
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 'OK',
        msg: '卸载成功',
        data: undefined,
      });
    }, 1000);
  });
}

// 为了兼容现有代码，保留一些便捷方法
export async function getMarketAppList(): Promise<ApiResponse<MarketApp[]>> {
  const params: MarketAppQueryParams = {
    pageNum: 1,
    pageSize: 1000, // 获取所有已上架的应用
  };
  const result = await getMarketAppPage(params);
  return {
    ...result,
    data: result.data.records,
  };
}
