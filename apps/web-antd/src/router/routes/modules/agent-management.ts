import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:users',
      keepAlive: true,
      order: 2,
      title: '代理商管理',
    },
    name: 'AgentManagement',
    path: '/agent-management',
    children: [
      {
        name: 'AgentList',
        path: 'list',
        component: () => import('#/views/agent-management/agent-list/index.vue'),
        meta: {
          affixTab: false,
          icon: 'lucide:building',
          title: '代理商列表',
        },
      },
      {
        name: 'MemberList',
        path: 'members',
        component: () => import('#/views/agent-management/member-list/index.vue'),
        meta: {
          affixTab: false,
          icon: 'lucide:user-check',
          title: '成员列表',
        },
      },
    ],
  },
];

export default routes;
